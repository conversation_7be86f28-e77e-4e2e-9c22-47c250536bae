graph TB
    subgraph "三层混合执行模式"
        A1[开发调试模式<br/>统一解释器<br/>实时调试+可视化]
        A2[优化执行模式<br/>统一编译器<br/>高性能执行]
        A3[智能优化模式<br/>JIT优化<br/>热点代码优化]
    end
    
    subgraph "核心组件架构"
        B1[UnifiedExecutionEngine<br/>统一执行引擎]
        B2[SharedExecutionContext<br/>共享执行上下文]
        B3[UnifiedInterpreter<br/>统一解释器]
        B4[UnifiedCompiler<br/>统一编译器]
    end
    
    subgraph "安全监控系统"
        C1[L1-执行安全<br/>看门狗+超时检测]
        C2[L2-逻辑安全<br/>死锁+循环检测]
        C3[L3-系统安全<br/>权限+沙箱执行]
        C4[L4-业务安全<br/>紧急停机+故障恢复]
    end
    
    subgraph "硬件抽象层"
        D1[IHardwareManager<br/>设备管理]
        D2[协议支持<br/>Modbus/OPC UA/Ethernet IP]
        D3[I/O处理<br/>异步读写]
        D4[事件通信<br/>实时监控]
    end
    
    subgraph "扫描周期"
        E1[高优先级 1ms<br/>安全关键]
        E2[中优先级 10ms<br/>控制逻辑]
        E3[低优先级 100ms<br/>监控诊断]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B1
    
    B1 --> B2
    B1 --> B3
    B1 --> B4
    
    B1 --> C1
    C1 --> C2
    C2 --> C3
    C3 --> C4
    
    B2 --> D1
    D1 --> D2
    D1 --> D3
    D1 --> D4
    
    B1 --> E1
    E1 --> E2
    E2 --> E3
    
    style A1 fill:#e1f5fe
    style A2 fill:#f3e5f5
    style A3 fill:#fff3e0
    style B1 fill:#e8f5e8
    style C1 fill:#ffebee
    style D1 fill:#f1f8e9
    style E1 fill:#fce4ec
