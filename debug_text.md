[InsertSelectionBranchAfterStep] 选中元素: 初始步
[选择分支] 首次插入，现有分支: 0个
[选择分支] 新分支位置: 221,319.5
[选择分支] 创建: Initial, 位置: 221,319.5
[分支集合] 添加分支，位置: 221,319.5
[分支集合] ViewModel位置: 221,319.5
[SFCCanvas] 为新创建的分支添加位置变化监听: 1f13a602-7cd4-48f1-834e-0f6bddf3e69a
[AddConnection] 开始执行: b0afb263-e2b8-42fc-9fb4-f84746f4fa47 -> 1f13a602-7cd4-48f1-834e-0f6bddf3e69a
[AddConnection] 对象查找结果: sourceObject=SFCStepModel, targetObject=SFCBranchModel
[AddConnection] 位置获取: 源ViewModel位置=200,200, 源Model位置=200,200
[AddConnection] 位置获取: 目标ViewModel位置=221,319.5, 目标Model位置=221,319.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Selection, 位置=221,319.5, 输出点=False
[对比调试] 如果是步骤输入点，位置应该是: 271,325.5
[CalculateElementConnectPoint] ✅ 选择分支左上连接点(索引0): 242,326.5
[AddConnection] 创建连接: b0afb263-e2b8-42fc-9fb4-f84746f4fa47 -> 1f13a602-7cd4-48f1-834e-0f6bddf3e69a
[AddConnection] 源位置: 200,200, 目标位置: 221,319.5
[AddConnection] 源对象类型: SFCStepModel, 目标对象类型: SFCBranchModel
[AddConnection] 源连接点: 250,322, 目标连接点: 242,326.5
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 ae5623a1-8987-4301-b165-9b1d2d4695c5 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 250,322, 终点: 242,326.5
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCStepViewModel, IsInput: False, ConnectionId: ae5623a1-8987-4301-b165-9b1d2d4695c5
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, IsInput: True, ConnectionId: ae5623a1-8987-4301-b165-9b1d2d4695c5
[UpdateConnectPointStates] 连接点状态更新完成: b0afb263-e2b8-42fc-9fb4-f84746f4fa47[0] -> 1f13a602-7cd4-48f1-834e-0f6bddf3e69a[0]
[选择分支] 跳过垂直对齐，保持计算位置: 221,319.5
[选择分支] 左分支使用竖直连接线，无需创建转换条件
[选择分支] 右分支转换条件已内嵌在选择分支视图中，无需单独创建
System.Windows.Data Error: 40 : BindingExpression path error: 'Description' property not found on 'object' ''SFCBranchViewModel' (HashCode=41156139)'. BindingExpression:Path=SelectedElement.Description; DataItem='EnhancedSFCViewModel' (HashCode=2142393); target element is 'TextBlock' (Name=''); target property is 'Text' (type 'String')
[InitializeConnectPoints] 开始初始化连接点，ViewModel.Id: 1f13a602-7cd4-48f1-834e-0f6bddf3e69a
[InitializeConnectPoints] XAML连接点查找结果: LeftTop=True, LeftBottom=True, RightTop=True, RightBottom=True
[InitializeConnectPoints] LeftTop连接点ElementId: '', 期望: '1f13a602-7cd4-48f1-834e-0f6bddf3e69a'
[InitializeConnectPoints] ✅ 已修复LeftTop连接点ElementId: '1f13a602-7cd4-48f1-834e-0f6bddf3e69a'
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=41156139)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=41156139); target element is 'SFCConnectPoint' (Name='LeftTopConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=41156139)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=41156139); target element is 'SFCConnectPoint' (Name='LeftBottomConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=41156139)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=41156139); target element is 'SFCConnectPoint' (Name='TopConnectPoint1'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=41156139)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=41156139); target element is 'SFCConnectPoint' (Name='TopConnectPoint2'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=41156139)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=41156139); target element is 'SFCConnectPoint' (Name='LeftTopConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=41156139)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=41156139); target element is 'SFCConnectPoint' (Name='LeftParallelPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=41156139)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=41156139); target element is 'SFCConnectPoint' (Name='RightParallelPoint'); target property is 'NoTarget' (type 'Object')
[InitializeConnectPoints] 连接点已初始化，跳过重复初始化: 1f13a602-7cd4-48f1-834e-0f6bddf3e69a
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSelectionBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSiblingBranchCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertParallelBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
[连接点重叠检测] 点1: (250.0, 322.0), 点2: (242.0, 326.5), 距离: 9.2px, 重叠: True
[CreateConnectionPath] 连接 ae5623a1-8987-4301-b165-9b1d2d4695c5 连接点重叠，隐藏连接线
[CreateConnectionPath] 起点: (250.0, 322.0), 终点: (242.0, 326.5)
连接 ae5623a1-8987-4301-b165-9b1d2d4695c5 连接点重叠，使用连接点重叠方式
[AddConnection] 延迟更新连接线 ae5623a1-8987-4301-b165-9b1d2d4695c5 的路径点
[CalculateElementConnectPoint] ✅ 选择分支ViewModel左上连接点(索引0): 250,326.5
[AddConnection] 延迟更新 - 源对象类型: SFCStepViewModel, 目标对象类型: SFCBranchViewModel
[AddConnection] 延迟更新 - 源位置: 200,200, 目标位置: 221,319.5
[AddConnection] 延迟更新后的路径点: 250,322 -> 250,326.5
[AddConnection] PathPoints集合已更新，应该触发重新评估
[SFCCanvas] 位置变化: SFCBranchViewModel.IsSelected
[SFCCanvas] 位置变化: SFCBranchViewModel.SelectedPart
[SFCSelectionBranchView] 右侧区域选中并开始拖拽: 1f13a602-7cd4-48f1-834e-0f6bddf3e69a
[SFCCanvas] 位置变化: SFCBranchViewModel.IsSelected
System.Windows.Data Error: 40 : BindingExpression path error: 'Description' property not found on 'object' ''SFCBranchViewModel' (HashCode=41156139)'. BindingExpression:Path=SelectedElement.Description; DataItem='EnhancedSFCViewModel' (HashCode=2142393); target element is 'TextBlock' (Name=''); target property is 'Text' (type 'String')
[InsertStepAfterSelected] 选中元素: 选择分支1(右侧转换条件)
[InsertStepAfterSelected] 选中元素位置: 221,319.5
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[GetActualConnectPointPosition] 分支连接点: 类型Selection, 索引3, 位置372.5,385.5
[CalculateConnectPointAlignedPositionDynamic] 动态对齐计算: 连接点372.5,385.5, 目标位置322.5,405.5
[InsertStepAfterSelected] 选择分支右侧使用动态计算: 连接点索引3, 步骤位置322.5,405.5
[InsertStepAfterSelected] 新步骤位置: 322.5,405.5
[InsertStepAfterSelected] 新步骤创建: fe0f50d4-6ad3-4479-943a-6704016036e3
[SFCCanvas] 为新创建的步骤添加位置变化监听: fe0f50d4-6ad3-4479-943a-6704016036e3
[InsertStepAfterSelected] 步骤已添加到集合
[InsertStepAfterSelected] 准备创建连接: 1f13a602-7cd4-48f1-834e-0f6bddf3e69a -> fe0f50d4-6ad3-4479-943a-6704016036e3
[InsertStepAfterSelected] 使用选择分支右侧T1连接点: 1f13a602-7cd4-48f1-834e-0f6bddf3e69a[索引3]
[InsertStepAfterSelected] 开始创建连接: 1f13a602-7cd4-48f1-834e-0f6bddf3e69a -> fe0f50d4-6ad3-4479-943a-6704016036e3, 源索引: 3
[AddConnection] 开始执行: 1f13a602-7cd4-48f1-834e-0f6bddf3e69a -> fe0f50d4-6ad3-4479-943a-6704016036e3
[AddConnection] 对象查找结果: sourceObject=SFCBranchModel, targetObject=SFCStepModel
[AddConnection] 位置获取: 源ViewModel位置=221,319.5, 源Model位置=221,319.5
[AddConnection] 位置获取: 目标ViewModel位置=322.5,405.5, 目标Model位置=322.5,405.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Selection, 位置=221,319.5, 输出点=True
[CalculateElementConnectPoint] ✅ 选择分支Model T1转换条件下连接点(索引3): 372.5,385.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[AddConnection] 创建连接: 1f13a602-7cd4-48f1-834e-0f6bddf3e69a -> fe0f50d4-6ad3-4479-943a-6704016036e3
[AddConnection] 源位置: 221,319.5, 目标位置: 322.5,405.5
[AddConnection] 源对象类型: SFCBranchModel, 目标对象类型: SFCStepModel
[AddConnection] 源连接点: 372.5,385.5, 目标连接点: 372.5,411.5
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 4bb47862-a848-44cf-811c-52353d286aeb 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 372.5,385.5, 终点: 372.5,411.5
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, IsInput: False, ConnectionId: 4bb47862-a848-44cf-811c-52353d286aeb
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCStepViewModel, IsInput: True, ConnectionId: 4bb47862-a848-44cf-811c-52353d286aeb
[UpdateConnectPointStates] 连接点状态更新完成: 1f13a602-7cd4-48f1-834e-0f6bddf3e69a[3] -> fe0f50d4-6ad3-4479-943a-6704016036e3[0]
[InsertStepAfterSelected] ✅ 连接创建成功: 4bb47862-a848-44cf-811c-52353d286aeb
[CreateConnectionPath] 连接 4bb47862-a848-44cf-811c-52353d286aeb 连接点不重叠，创建连接线
[CreateConnectionPath] 起点: (372.5, 385.5), 终点: (372.5, 411.5), 距离: 26.0px
[贝塞尔曲线创建] 起点: (372.5, 385.5), 终点: (372.5, 411.5)
[贝塞尔曲线创建] Y差异: 26.0px, X差异: 0.0px, 控制点偏移: 10.4px
[贝塞尔曲线创建] PathPoints[0]: (372.5, 385.5), PathPoints[1]: (372.5, 411.5)
添加连接线: 4bb47862-a848-44cf-811c-52353d286aeb
[AddConnection] 延迟更新连接线 4bb47862-a848-44cf-811c-52353d286aeb 的路径点
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[AddConnection] 延迟更新 - 源对象类型: SFCBranchViewModel, 目标对象类型: SFCStepViewModel
[AddConnection] 延迟更新 - 源位置: 221,319.5, 目标位置: 322.5,405.5
[AddConnection] 延迟更新后的路径点: 372.5,385.5 -> 372.5,411.5
[AddConnection] PathPoints集合已更新，应该触发重新评估
线程 '.NET TP Worker' (7244) 已退出，返回值为 0 (0x0)。
线程 '.NET TP Worker' (29852) 已退出，返回值为 0 (0x0)。
线程 '.NET TP Worker' (8896) 已退出，返回值为 0 (0x0)。
线程 '.NET TP Worker' (26904) 已退出，返回值为 0 (0x0)。
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 322.5,405.5
[SFCCanvas] 位置变化: SFCStepViewModel.IsSelected
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 323.3727272727273,405.5
[SFCCanvas] 位置变化: SFCStepViewModel.Position
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 373.3727272727273,411.5
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 325.1181818181818,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 375.1181818181818,411.5
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 326.8636363636364,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 376.8636363636364,411.5
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 327.73636363636365,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 377.73636363636365,411.5
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 328.6090909090909,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 378.6090909090909,411.5
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 329.4818181818182,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 379.4818181818182,411.5
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 330.3545454545455,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 380.3545454545455,411.5
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 330.3545454545455,405.5
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 331.22727272727275,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 381.22727272727275,411.5
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 331.22727272727275,405.5
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 332.1,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 382.1,411.5
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 332.1,405.5
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 332.9727272727273,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 382.9727272727273,411.5
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 332.9727272727273,405.5
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 333.8454545454546,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 383.8454545454546,411.5
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 333.8454545454546,406.3727272727273
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 383.8454545454546,412.3727272727273
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 333.8454545454546,406.3727272727273
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 334.71818181818185,406.3727272727273
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 384.71818181818185,412.3727272727273
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 334.71818181818185,406.3727272727273
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 335.5909090909091,406.3727272727273
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 385.5909090909091,412.3727272727273
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 336.4636363636364,407.24545454545455
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 386.4636363636364,413.24545454545455
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 336.4636363636364,407.24545454545455
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 337.33636363636367,407.24545454545455
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 387.33636363636367,413.24545454545455
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 337.33636363636367,407.24545454545455
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 338.20909090909095,407.24545454545455
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 388.20909090909095,413.24545454545455
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 338.20909090909095,407.24545454545455
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 338.20909090909095,408.1181818181818
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 388.20909090909095,414.1181818181818
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 338.20909090909095,408.11818181818194
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 388.20909090909095,414.11818181818194
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 339.0818181818182,408.11818181818205
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 389.0818181818182,414.11818181818205
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 339.0818181818182,408.11818181818217
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 389.0818181818182,414.11818181818217
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 339.0818181818182,408.99090909090955
[SFCCanvas] 位置变化: SFCStepViewModel.Position
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 389.0818181818182,414.99090909090955
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 339.9545454545455,408.99090909090955
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 389.9545454545455,414.99090909090955
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 339.9545454545455,410.7363636363641
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 389.9545454545455,416.7363636363641
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 340.82727272727277,411.6090909090914
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 390.82727272727277,417.6090909090914
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 341.70000000000005,412.48181818181877
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 391.70000000000005,418.48181818181877
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 342.5727272727273,414.2272727272733
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 392.5727272727273,420.2272727272733
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 342.5727272727273,415.1000000000006
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 392.5727272727273,421.1000000000006
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 343.4454545454546,415.972727272728
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 393.4454545454546,421.972727272728
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 344.31818181818187,417.71818181818253
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 394.31818181818187,423.71818181818253
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 345.19090909090914,418.5909090909098
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 395.19090909090914,424.5909090909098
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 346.0636363636364,421.20909090909174
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 396.0636363636364,427.20909090909174
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 346.9363636363637,422.081818181819
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 396.9363636363637,428.081818181819
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 348.68181818181824,424.70000000000095
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 398.68181818181824,430.70000000000095
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 348.68181818181824,424.70000000000095
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 349.5545454545455,425.57272727272823
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 399.5545454545455,431.57272727272823
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 349.5545454545455,426.4454545454556
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 399.5545454545455,432.4454545454556
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 349.5545454545455,427.3181818181829
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 399.5545454545455,433.3181818181829
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 350.4272727272727,427.318181818183
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 400.4272727272727,433.318181818183
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 350.42727272727257,428.1909090909104
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 400.42727272727257,434.1909090909104
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 350.42727272727245,428.1909090909104
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 400.42727272727245,434.1909090909104
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 350.42727272727234,429.06363636363767
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 400.42727272727234,435.06363636363767
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 350.4272727272722,429.0636363636378
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 400.4272727272722,435.0636363636378
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 350.4272727272721,429.9363636363652
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 400.4272727272721,435.9363636363652
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 350.427272727272,430.80909090909245
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 400.427272727272,436.80909090909245
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 351.29999999999916,430.80909090909256
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 401.29999999999916,436.80909090909256
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 351.29999999999905,431.68181818181995
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 401.29999999999905,437.68181818181995
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 351.29999999999893,431.68181818182006
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 401.29999999999893,437.68181818182006
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 351.2999999999988,432.55454545454745
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 401.2999999999988,438.55454545454745
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 351.2999999999987,433.42727272727484
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 401.2999999999987,439.42727272727484
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 351.2999999999986,433.42727272727495
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 401.2999999999986,439.42727272727495
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 351.2999999999985,434.30000000000234
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 401.2999999999985,440.30000000000234
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 4bb47862-a848-44cf-811c-52353d286aeb 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: fe0f50d4-6ad3-4479-943a-6704016036e3 -> 351.29999999999836,434.30000000000246
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 372.5,385.5
[连接点调试] 源连接点计算位置: 372.5,385.5
[连接点调试] 目标连接点计算位置: 401.29999999999836,440.30000000000246