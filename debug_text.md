[InsertSelectionBranchAfterStep] 选中元素: 初始步
[选择分支] 首次插入，现有分支: 0个
[选择分支] 新分支位置: 221,319.5
[选择分支] 创建: Initial, 位置: 221,319.5
[分支集合] 添加分支，位置: 221,319.5
[分支集合] ViewModel位置: 221,319.5
[SFCCanvas] 为新创建的分支添加位置变化监听: 64b80416-8612-40d1-83a9-7bb29611c205
[AddConnection] 开始执行: 3cd01047-7a79-466c-9090-0a41496719f0 -> 64b80416-8612-40d1-83a9-7bb29611c205
[AddConnection] 对象查找结果: sourceObject=SFCStepModel, targetObject=SFCBranchModel
[AddConnection] 位置获取: 源ViewModel位置=200,200, 源Model位置=200,200
[AddConnection] 位置获取: 目标ViewModel位置=221,319.5, 目标Model位置=221,319.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Selection, 位置=221,319.5, 输出点=False
[对比调试] 如果是步骤输入点，位置应该是: 271,325.5
[CalculateElementConnectPoint] ✅ 选择分支左上连接点(索引0): 242,326.5
[AddConnection] 创建连接: 3cd01047-7a79-466c-9090-0a41496719f0 -> 64b80416-8612-40d1-83a9-7bb29611c205
[AddConnection] 源位置: 200,200, 目标位置: 221,319.5
[AddConnection] 源对象类型: SFCStepModel, 目标对象类型: SFCBranchModel
[AddConnection] 源连接点: 250,322, 目标连接点: 242,326.5
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 f32b5b71-4a1e-4ce2-b29c-08fab393a96b 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 250,322, 终点: 242,326.5
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCStepViewModel, IsInput: False, ConnectionId: f32b5b71-4a1e-4ce2-b29c-08fab393a96b
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, IsInput: True, ConnectionId: f32b5b71-4a1e-4ce2-b29c-08fab393a96b
[UpdateConnectPointStates] 连接点状态更新完成: 3cd01047-7a79-466c-9090-0a41496719f0[0] -> 64b80416-8612-40d1-83a9-7bb29611c205[0]
[选择分支] 跳过垂直对齐，保持计算位置: 221,319.5
[选择分支] 左分支使用竖直连接线，无需创建转换条件
[选择分支] 右分支转换条件已内嵌在选择分支视图中，无需单独创建
System.Windows.Data Error: 40 : BindingExpression path error: 'Description' property not found on 'object' ''SFCBranchViewModel' (HashCode=36953614)'. BindingExpression:Path=SelectedElement.Description; DataItem='EnhancedSFCViewModel' (HashCode=2142393); target element is 'TextBlock' (Name=''); target property is 'Text' (type 'String')
[InitializeConnectPoints] 开始初始化连接点，ViewModel.Id: 64b80416-8612-40d1-83a9-7bb29611c205
[InitializeConnectPoints] XAML连接点查找结果: LeftTop=True, LeftBottom=True, RightTop=True, RightBottom=True
[InitializeConnectPoints] LeftTop连接点ElementId: '', 期望: '64b80416-8612-40d1-83a9-7bb29611c205'
[InitializeConnectPoints] ✅ 已修复LeftTop连接点ElementId: '64b80416-8612-40d1-83a9-7bb29611c205'
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=36953614)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=36953614); target element is 'SFCConnectPoint' (Name='LeftTopConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=36953614)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=36953614); target element is 'SFCConnectPoint' (Name='LeftBottomConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=36953614)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=36953614); target element is 'SFCConnectPoint' (Name='TopConnectPoint1'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=36953614)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=36953614); target element is 'SFCConnectPoint' (Name='TopConnectPoint2'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=36953614)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=36953614); target element is 'SFCConnectPoint' (Name='LeftTopConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=36953614)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=36953614); target element is 'SFCConnectPoint' (Name='LeftParallelPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=36953614)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=36953614); target element is 'SFCConnectPoint' (Name='RightParallelPoint'); target property is 'NoTarget' (type 'Object')
[InitializeConnectPoints] 连接点已初始化，跳过重复初始化: 64b80416-8612-40d1-83a9-7bb29611c205
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSelectionBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSiblingBranchCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertParallelBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
[连接点重叠检测] 点1: (250.0, 322.0), 点2: (242.0, 326.5), 距离: 9.2px, 重叠: True
[CreateConnectionPath] 连接 f32b5b71-4a1e-4ce2-b29c-08fab393a96b 连接点重叠，隐藏连接线
[CreateConnectionPath] 起点: (250.0, 322.0), 终点: (242.0, 326.5)
连接 f32b5b71-4a1e-4ce2-b29c-08fab393a96b 连接点重叠，使用连接点重叠方式
[AddConnection] 延迟更新连接线 f32b5b71-4a1e-4ce2-b29c-08fab393a96b 的路径点
[CalculateElementConnectPoint] ✅ 选择分支ViewModel左上连接点(索引0): 250,326.5
[AddConnection] 延迟更新 - 源对象类型: SFCStepViewModel, 目标对象类型: SFCBranchViewModel
[AddConnection] 延迟更新 - 源位置: 200,200, 目标位置: 221,319.5
[AddConnection] 延迟更新后的路径点: 250,322 -> 250,326.5
[AddConnection] PathPoints集合已更新，应该触发重新评估
[SFCCanvas] 位置变化: SFCBranchViewModel.IsSelected
[SFCCanvas] 位置变化: SFCBranchViewModel.SelectedPart
[SFCSelectionBranchView] 右侧区域选中并开始拖拽: 64b80416-8612-40d1-83a9-7bb29611c205
[SFCCanvas] 位置变化: SFCBranchViewModel.IsSelected
System.Windows.Data Error: 40 : BindingExpression path error: 'Description' property not found on 'object' ''SFCBranchViewModel' (HashCode=36953614)'. BindingExpression:Path=SelectedElement.Description; DataItem='EnhancedSFCViewModel' (HashCode=2142393); target element is 'TextBlock' (Name=''); target property is 'Text' (type 'String')
[InsertStepAfterSelected] 选中元素: 选择分支1(右侧转换条件)
[InsertStepAfterSelected] 选中元素位置: 221,319.5
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[GetActualConnectPointPosition] 分支连接点: 类型Selection, 索引3, 位置388,385.5
[CalculateConnectPointAlignedPositionDynamic] 动态对齐计算: 连接点388,385.5, 目标位置338,405.5
[InsertStepAfterSelected] 选择分支右侧使用动态计算: 连接点索引3, 步骤位置338,405.5
[InsertStepAfterSelected] 新步骤位置: 338,405.5
[InsertStepAfterSelected] 新步骤创建: a641942b-3257-482e-81de-1467b08f8dfd
[SFCCanvas] 为新创建的步骤添加位置变化监听: a641942b-3257-482e-81de-1467b08f8dfd
[InsertStepAfterSelected] 步骤已添加到集合
[InsertStepAfterSelected] 准备创建连接: 64b80416-8612-40d1-83a9-7bb29611c205 -> a641942b-3257-482e-81de-1467b08f8dfd
[InsertStepAfterSelected] 使用选择分支右侧T1连接点: 64b80416-8612-40d1-83a9-7bb29611c205[索引3]
[InsertStepAfterSelected] 开始创建连接: 64b80416-8612-40d1-83a9-7bb29611c205 -> a641942b-3257-482e-81de-1467b08f8dfd, 源索引: 3
[AddConnection] 开始执行: 64b80416-8612-40d1-83a9-7bb29611c205 -> a641942b-3257-482e-81de-1467b08f8dfd
[AddConnection] 对象查找结果: sourceObject=SFCBranchModel, targetObject=SFCStepModel
[AddConnection] 位置获取: 源ViewModel位置=221,319.5, 源Model位置=221,319.5
[AddConnection] 位置获取: 目标ViewModel位置=338,405.5, 目标Model位置=338,405.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Selection, 位置=221,319.5, 输出点=True
[CalculateElementConnectPoint] ✅ 选择分支Model右侧下端连接点(索引3): 388,385.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[AddConnection] 创建连接: 64b80416-8612-40d1-83a9-7bb29611c205 -> a641942b-3257-482e-81de-1467b08f8dfd
[AddConnection] 源位置: 221,319.5, 目标位置: 338,405.5
[AddConnection] 源对象类型: SFCBranchModel, 目标对象类型: SFCStepModel
[AddConnection] 源连接点: 388,385.5, 目标连接点: 388,411.5
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 388,385.5, 终点: 388,411.5
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, IsInput: False, ConnectionId: 270c385d-a59f-4462-a8d2-03cfb17ac752
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCStepViewModel, IsInput: True, ConnectionId: 270c385d-a59f-4462-a8d2-03cfb17ac752
[UpdateConnectPointStates] 连接点状态更新完成: 64b80416-8612-40d1-83a9-7bb29611c205[3] -> a641942b-3257-482e-81de-1467b08f8dfd[0]
[InsertStepAfterSelected] ✅ 连接创建成功: 270c385d-a59f-4462-a8d2-03cfb17ac752
[CreateConnectionPath] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 连接点不重叠，创建连接线
[CreateConnectionPath] 起点: (388.0, 385.5), 终点: (388.0, 411.5), 距离: 26.0px
[贝塞尔曲线创建] 起点: (388.0, 385.5), 终点: (388.0, 411.5)
[贝塞尔曲线创建] Y差异: 26.0px, X差异: 0.0px, 控制点偏移: 10.4px
[贝塞尔曲线创建] PathPoints[0]: (388.0, 385.5), PathPoints[1]: (388.0, 411.5)
添加连接线: 270c385d-a59f-4462-a8d2-03cfb17ac752
[AddConnection] 延迟更新连接线 270c385d-a59f-4462-a8d2-03cfb17ac752 的路径点
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[AddConnection] 延迟更新 - 源对象类型: SFCBranchViewModel, 目标对象类型: SFCStepViewModel
[AddConnection] 延迟更新 - 源位置: 221,319.5, 目标位置: 338,405.5
[AddConnection] 延迟更新后的路径点: 388,385.5 -> 388,411.5
[AddConnection] PathPoints集合已更新，应该触发重新评估
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 338,405.5
[SFCCanvas] 位置变化: SFCStepViewModel.IsSelected
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 337.48663101604274,405.5
[SFCCanvas] 位置变化: SFCStepViewModel.Position
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 387.48663101604274,411.5
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 335.94652406417106,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 385.94652406417106,411.5
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 335.6385026737968,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 385.6385026737968,411.5
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 334.30374331550803,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 384.30374331550803,411.5
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 334.20106951871657,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 384.20106951871657,411.5
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 333.5850267379679,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 383.5850267379679,411.5
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 333.3283422459893,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 383.3283422459893,411.5
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 332.5582887700534,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 382.5582887700534,411.5
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 332.14759358288757,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 382.14759358288757,411.5
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 331.2235294117645,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 381.2235294117645,411.5
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 331.1721925133687,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 381.1721925133687,411.5
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 331.48021390374305,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 381.48021390374305,411.5
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 330.4021390374329,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 380.4021390374329,411.5
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 330.19679144385003,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 380.19679144385003,411.5
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 330.86417112299443,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 380.86417112299443,411.5
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 330.65882352941156,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 380.65882352941156,411.5
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 329.9401069518714,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 379.9401069518714,411.5
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 330.09411764705857,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 380.09411764705857,411.5
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 330.2481283422457,404.9866310160428
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 380.2481283422457,410.9866310160428
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 330.40213903743285,405.34598930481286
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 380.40213903743285,411.34598930481286
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 329.6834224598927,405.7053475935829
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 379.6834224598927,411.7053475935829
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 329.32406417112264,405.1919786096257
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 379.32406417112264,411.1919786096257
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 329.83743315507985,405.5513368983958
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 379.83743315507985,411.5513368983958
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 329.4780748663098,405.0379679144386
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 379.4780748663098,411.0379679144386
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 329.1187165775397,405.39732620320865
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 379.1187165775397,411.39732620320865
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 329.6320855614969,404.88395721925144
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 379.6320855614969,410.88395721925144
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 329.27272727272685,405.2433155080215
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 379.27272727272685,411.2433155080215
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 329.78609625668406,405.6026737967916
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 379.78609625668406,411.6026737967916
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 329.426737967914,405.08930481283437
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 379.426737967914,411.08930481283437
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 329.9401069518712,405.44866310160444
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 379.9401069518712,411.44866310160444
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 329.58074866310113,404.93529411764723
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 379.58074866310113,410.93529411764723
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 329.22139037433107,405.2946524064173
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 379.22139037433107,411.2946524064173
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 329.7347593582883,405.65401069518737
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 379.7347593582883,411.65401069518737
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 329.3754010695182,405.14064171123016
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 379.3754010695182,411.14064171123016
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 329.8887700534754,405.5000000000002
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 379.8887700534754,411.5000000000002
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 329.52941176470534,404.986631016043
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 379.52941176470534,410.986631016043
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 329.1700534759353,405.3459893048131
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 379.1700534759353,411.3459893048131
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 329.6834224598925,405.70534759358316
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 379.6834224598925,411.70534759358316
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 329.3240641711224,404.6786096256687
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 379.3240641711224,410.6786096256687
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 329.8374331550796,404.5245989304815
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 379.8374331550796,410.5245989304815
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 329.47807486630956,404.3705882352943
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 379.47807486630956,410.3705882352943
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 329.1187165775395,405.0893048128344
[CalculateElementConnectPoint] ✅ 选择分支右侧下端连接点(索引3): 388,385.5
[连接点调试] 源连接点计算位置: 388,385.5
[连接点调试] 目标连接点计算位置: 379.1187165775395,411.0893048128344
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 270c385d-a59f-4462-a8d2-03cfb17ac752 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: a641942b-3257-482e-81de-1467b08f8dfd -> 329.6320855614967,404.93529411764723