[InsertSelectionBranchAfterStep] 选中元素: 初始步
[选择分支] 首次插入，现有分支: 0个
[选择分支] 新分支位置: 221,319.5
[选择分支] 创建: Initial, 位置: 221,319.5
[分支集合] 添加分支，位置: 221,319.5
[分支集合] ViewModel位置: 221,319.5
[SFCCanvas] 为新创建的分支添加位置变化监听: dd2c2797-b9f8-4eb4-be84-d9de5d1e0e55
[AddConnection] 开始执行: af3777ea-4072-47c6-996b-68c6ef0df7ac -> dd2c2797-b9f8-4eb4-be84-d9de5d1e0e55
[AddConnection] 对象查找结果: sourceObject=SFCStepModel, targetObject=SFCBranchModel
[AddConnection] 位置获取: 源ViewModel位置=200,200, 源Model位置=200,200
[AddConnection] 位置获取: 目标ViewModel位置=221,319.5, 目标Model位置=221,319.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Selection, 位置=221,319.5, 输出点=False
[对比调试] 如果是步骤输入点，位置应该是: 271,325.5
[CalculateElementConnectPoint] ✅ 选择分支左上连接点(索引0): 242,326.5
[AddConnection] 创建连接: af3777ea-4072-47c6-996b-68c6ef0df7ac -> dd2c2797-b9f8-4eb4-be84-d9de5d1e0e55
[AddConnection] 源位置: 200,200, 目标位置: 221,319.5
[AddConnection] 源对象类型: SFCStepModel, 目标对象类型: SFCBranchModel
[AddConnection] 源连接点: 250,322, 目标连接点: 242,326.5
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 feb520f7-0082-44ca-868f-76525b287b6f 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 250,322, 终点: 242,326.5
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCStepViewModel, IsInput: False, ConnectionId: feb520f7-0082-44ca-868f-76525b287b6f
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, IsInput: True, ConnectionId: feb520f7-0082-44ca-868f-76525b287b6f
[UpdateConnectPointStates] 连接点状态更新完成: af3777ea-4072-47c6-996b-68c6ef0df7ac[0] -> dd2c2797-b9f8-4eb4-be84-d9de5d1e0e55[0]
[选择分支] 跳过垂直对齐，保持计算位置: 221,319.5
[选择分支] 左分支使用竖直连接线，无需创建转换条件
[选择分支] 右分支转换条件已内嵌在选择分支视图中，无需单独创建
System.Windows.Data Error: 40 : BindingExpression path error: 'Description' property not found on 'object' ''SFCBranchViewModel' (HashCode=56572634)'. BindingExpression:Path=SelectedElement.Description; DataItem='EnhancedSFCViewModel' (HashCode=2142393); target element is 'TextBlock' (Name=''); target property is 'Text' (type 'String')
[InitializeConnectPoints] 开始初始化连接点，ViewModel.Id: dd2c2797-b9f8-4eb4-be84-d9de5d1e0e55
[InitializeConnectPoints] XAML连接点查找结果: LeftTop=True, LeftBottom=True, RightTop=True, RightBottom=True
[InitializeConnectPoints] LeftTop连接点ElementId: '', 期望: 'dd2c2797-b9f8-4eb4-be84-d9de5d1e0e55'
[InitializeConnectPoints] ✅ 已修复LeftTop连接点ElementId: 'dd2c2797-b9f8-4eb4-be84-d9de5d1e0e55'
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=56572634)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=56572634); target element is 'SFCConnectPoint' (Name='LeftTopConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=56572634)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=56572634); target element is 'SFCConnectPoint' (Name='LeftBottomConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=56572634)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=56572634); target element is 'SFCConnectPoint' (Name='TopConnectPoint1'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=56572634)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=56572634); target element is 'SFCConnectPoint' (Name='TopConnectPoint2'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=56572634)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=56572634); target element is 'SFCConnectPoint' (Name='LeftTopConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=56572634)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=56572634); target element is 'SFCConnectPoint' (Name='LeftParallelPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=56572634)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=56572634); target element is 'SFCConnectPoint' (Name='RightParallelPoint'); target property is 'NoTarget' (type 'Object')
[InitializeConnectPoints] 连接点已初始化，跳过重复初始化: dd2c2797-b9f8-4eb4-be84-d9de5d1e0e55
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSelectionBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSiblingBranchCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertParallelBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
[连接点重叠检测] 点1: (250.0, 322.0), 点2: (242.0, 326.5), 距离: 9.2px, 重叠: True
[CreateConnectionPath] 连接 feb520f7-0082-44ca-868f-76525b287b6f 连接点重叠，隐藏连接线
[CreateConnectionPath] 起点: (250.0, 322.0), 终点: (242.0, 326.5)
连接 feb520f7-0082-44ca-868f-76525b287b6f 连接点重叠，使用连接点重叠方式
[AddConnection] 延迟更新连接线 feb520f7-0082-44ca-868f-76525b287b6f 的路径点
[CalculateElementConnectPoint] ✅ 选择分支ViewModel左上连接点(索引0): 250,326.5
[AddConnection] 延迟更新 - 源对象类型: SFCStepViewModel, 目标对象类型: SFCBranchViewModel
[AddConnection] 延迟更新 - 源位置: 200,200, 目标位置: 221,319.5
[AddConnection] 延迟更新后的路径点: 250,322 -> 250,326.5

[SFCCanvas] 位置变化: SFCBranchViewModel.IsSelected
[SFCCanvas] 位置变化: SFCBranchViewModel.SelectedPart
[SFCSelectionBranchView] 右侧区域选中并开始拖拽: dd2c2797-b9f8-4eb4-be84-d9de5d1e0e55
[SFCCanvas] 位置变化: SFCBranchViewModel.IsSelected
System.Windows.Data Error: 40 : BindingExpression path error: 'Description' property not found on 'object' ''SFCBranchViewModel' (HashCode=56572634)'. BindingExpression:Path=SelectedElement.Description; DataItem='EnhancedSFCViewModel' (HashCode=2142393); target element is 'TextBlock' (Name=''); target property is 'Text' (type 'String')
[InsertStepAfterSelected] 选中元素: 选择分支1(右侧转换条件)
[InsertStepAfterSelected] 选中元素位置: 221,319.5
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[GetActualConnectPointPosition] 分支连接点: 类型Selection, 索引3, 位置375,385.5
[CalculateConnectPointAlignedPositionDynamic] 动态对齐计算: 连接点375,385.5, 目标位置325,405.5
[InsertStepAfterSelected] 选择分支右侧使用动态计算: 连接点索引3, 步骤位置325,405.5
[InsertStepAfterSelected] 新步骤位置: 325,405.5
[InsertStepAfterSelected] 新步骤创建: bb35392d-56dc-4795-b714-1412a17d6bfa
[SFCCanvas] 为新创建的步骤添加位置变化监听: bb35392d-56dc-4795-b714-1412a17d6bfa
[InsertStepAfterSelected] 步骤已添加到集合
[InsertStepAfterSelected] 准备创建连接: dd2c2797-b9f8-4eb4-be84-d9de5d1e0e55 -> bb35392d-56dc-4795-b714-1412a17d6bfa
[InsertStepAfterSelected] 使用选择分支右侧T1连接点: dd2c2797-b9f8-4eb4-be84-d9de5d1e0e55[索引3]
[InsertStepAfterSelected] 开始创建连接: dd2c2797-b9f8-4eb4-be84-d9de5d1e0e55 -> bb35392d-56dc-4795-b714-1412a17d6bfa, 源索引: 3
[AddConnection] 开始执行: dd2c2797-b9f8-4eb4-be84-d9de5d1e0e55 -> bb35392d-56dc-4795-b714-1412a17d6bfa
[AddConnection] 对象查找结果: sourceObject=SFCBranchModel, targetObject=SFCStepModel
[AddConnection] 位置获取: 源ViewModel位置=221,319.5, 源Model位置=221,319.5
[AddConnection] 位置获取: 目标ViewModel位置=325,405.5, 目标Model位置=325,405.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Selection, 位置=221,319.5, 输出点=True
[CalculateElementConnectPoint] ✅ 选择分支右下连接点(索引3): 388,385.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[AddConnection] 创建连接: dd2c2797-b9f8-4eb4-be84-d9de5d1e0e55 -> bb35392d-56dc-4795-b714-1412a17d6bfa
[AddConnection] 源位置: 221,319.5, 目标位置: 325,405.5
[AddConnection] 源对象类型: SFCBranchModel, 目标对象类型: SFCStepModel
[AddConnection] 源连接点: 388,385.5, 目标连接点: 375,411.5
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 388,385.5, 终点: 375,411.5
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, IsInput: False, ConnectionId: 26ce30f2-8f18-4ff5-b57d-4eac6d063601
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCStepViewModel, IsInput: True, ConnectionId: 26ce30f2-8f18-4ff5-b57d-4eac6d063601
[UpdateConnectPointStates] 连接点状态更新完成: dd2c2797-b9f8-4eb4-be84-d9de5d1e0e55[3] -> bb35392d-56dc-4795-b714-1412a17d6bfa[0]
[InsertStepAfterSelected] ✅ 连接创建成功: 26ce30f2-8f18-4ff5-b57d-4eac6d063601
[CreateConnectionPath] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 连接点不重叠，创建连接线
[CreateConnectionPath] 起点: (388.0, 385.5), 终点: (375.0, 411.5), 距离: 29.1px
[贝塞尔曲线创建] 起点: (388.0, 385.5), 终点: (375.0, 411.5)
[贝塞尔曲线创建] Y差异: 26.0px, X差异: 13.0px, 控制点偏移: 10.4px
[贝塞尔曲线创建] PathPoints[0]: (388.0, 385.5), PathPoints[1]: (375.0, 411.5)
添加连接线: 26ce30f2-8f18-4ff5-b57d-4eac6d063601
[AddConnection] 延迟更新连接线 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的路径点
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[AddConnection] 延迟更新 - 源对象类型: SFCBranchViewModel, 目标对象类型: SFCStepViewModel
[AddConnection] 延迟更新 - 源位置: 221,319.5, 目标位置: 325,405.5
[AddConnection] 延迟更新后的路径点: 375,385.5 -> 375,411.5
[AddConnection] PathPoints集合已更新，应该触发重新评估
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 325,405.5
[SFCCanvas] 位置变化: SFCStepViewModel.IsSelected
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 325.8727272727273,405.5
[SFCCanvas] 位置变化: SFCStepViewModel.Position
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 375.8727272727273,411.5
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 328.4909090909091,406.3727272727273
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 378.4909090909091,412.3727272727273
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 330.23636363636365,407.24545454545444
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 380.23636363636365,413.24545454545444
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 331.1090909090909,407.2454545454543
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 381.1090909090909,413.2454545454543
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 333.72727272727275,407.2454545454542
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 383.72727272727275,413.2454545454542
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 334.6,407.2454545454541
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 384.6,413.2454545454541
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 337.21818181818185,408.11818181818126
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 387.21818181818185,414.11818181818126
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 339.83636363636356,408.11818181818126
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 389.83636363636356,414.11818181818126
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 342.45454545454527,408.99090909090853
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 392.45454545454527,414.99090909090853
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 345.072727272727,408.9909090909084
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 395.072727272727,414.9909090909084
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 346.8181818181814,408.9909090909083
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 396.8181818181814,414.9909090909083
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 348.56363636363585,408.9909090909082
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 398.56363636363585,414.9909090909082
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 351.18181818181756,409.86363636363535
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 401.18181818181756,415.86363636363535
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 353.7999999999993,409.86363636363535
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 403.7999999999993,415.86363636363535
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 354.67272727272643,409.86363636363535
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 404.67272727272643,415.86363636363535
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 356.41818181818087,409.86363636363535
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 406.41818181818087,415.86363636363535
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 358.1636363636353,409.86363636363535
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 408.1636363636353,415.86363636363535
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 359.90909090908985,409.86363636363535
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 409.90909090908985,415.86363636363535
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 359.90909090908985,409.86363636363535
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 360.78181818181713,409.86363636363535
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 410.78181818181713,415.86363636363535
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 360.78181818181713,409.86363636363535
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 361.6545454545444,409.86363636363535
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 411.6545454545444,415.86363636363535
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 362.5272727272717,409.86363636363535
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 412.5272727272717,415.86363636363535
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 362.5272727272717,409.86363636363535
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 363.39999999999895,409.86363636363535
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 413.39999999999895,415.86363636363535
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 363.39999999999895,409.86363636363535
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 364.27272727272623,409.86363636363535
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 414.27272727272623,415.86363636363535
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 364.27272727272623,409.86363636363535
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 365.1454545454535,409.86363636363535
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 415.1454545454535,415.86363636363535
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 366.0181818181808,409.86363636363535
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 416.0181818181808,415.86363636363535
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 366.0181818181808,409.86363636363535
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 366.89090909090805,409.86363636363535
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 416.89090909090805,415.86363636363535
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 367.7636363636353,409.86363636363535
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 417.7636363636353,415.86363636363535
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 369.5090909090899,409.86363636363535
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 419.5090909090899,415.86363636363535
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 26ce30f2-8f18-4ff5-b57d-4eac6d063601 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: bb35392d-56dc-4795-b714-1412a17d6bfa -> 371.2545454545444,409.86363636363535