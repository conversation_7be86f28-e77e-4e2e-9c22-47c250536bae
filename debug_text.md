[SFCCanvas] 控件加载完成，当前DataContext: EnhancedSFCViewModel
[SFCCanvas] 已订阅步骤位置变化: f3eddfc5-0024-42b1-b99e-de35e30c3be9
[SFCCanvas] 已订阅ViewModel位置变化事件 - 步骤:1, 转换:0, 分支:0
[SFCStepView] 拖动更新位置: f3eddfc5-0024-42b1-b99e-de35e30c3be9 -> 200,200
[SFCCanvas] 位置变化: SFCStepViewModel.IsSelected
“PC_Control2.Demo.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.NETCore.App\8.0.17\System.Runtime.Intrinsics.dll”。已跳过加载符号。模块进行了优化，并且调试器选项“仅我的代码”已启用。
“PC_Control2.Demo.exe”(CoreCLR: clrhost): 已加载“C:\Program Files\dotnet\shared\Microsoft.WindowsDesktop.App\8.0.17\Accessibility.dll”。模块已生成，不包含符号。
[InsertSelectionBranchAfterStep] 选中元素: 初始步
[选择分支] 首次插入，现有分支: 0个
[选择分支] 新分支位置: 221,319.5
[选择分支] 创建: Initial, 位置: 221,319.5
[分支集合] 添加分支，位置: 221,319.5
[分支集合] ViewModel位置: 221,319.5
[SFCCanvas] 为新创建的分支添加位置变化监听: 9a712f73-0a34-408f-82d6-43eb465bd577
[AddConnection] 开始执行: f3eddfc5-0024-42b1-b99e-de35e30c3be9 -> 9a712f73-0a34-408f-82d6-43eb465bd577
[AddConnection] 对象查找结果: sourceObject=SFCStepModel, targetObject=SFCBranchModel
[AddConnection] 位置获取: 源ViewModel位置=200,200, 源Model位置=200,200
[AddConnection] 位置获取: 目标ViewModel位置=221,319.5, 目标Model位置=221,319.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Selection, 位置=221,319.5, 输出点=False
[对比调试] 如果是步骤输入点，位置应该是: 271,325.5
[CalculateElementConnectPoint] ✅ 选择分支左上连接点(索引0): 242,326.5
[AddConnection] 创建连接: f3eddfc5-0024-42b1-b99e-de35e30c3be9 -> 9a712f73-0a34-408f-82d6-43eb465bd577
[AddConnection] 源位置: 200,200, 目标位置: 221,319.5
[AddConnection] 源对象类型: SFCStepModel, 目标对象类型: SFCBranchModel
[AddConnection] 源连接点: 250,322, 目标连接点: 242,326.5
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 cdf77451-f16b-4a2a-8eb6-d69fc8a31f73 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 250,322, 终点: 242,326.5
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCStepViewModel, IsInput: False, ConnectionId: cdf77451-f16b-4a2a-8eb6-d69fc8a31f73
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, IsInput: True, ConnectionId: cdf77451-f16b-4a2a-8eb6-d69fc8a31f73
[UpdateConnectPointStates] 连接点状态更新完成: f3eddfc5-0024-42b1-b99e-de35e30c3be9[0] -> 9a712f73-0a34-408f-82d6-43eb465bd577[0]
[选择分支] 跳过垂直对齐，保持计算位置: 221,319.5
[选择分支] 左分支使用竖直连接线，无需创建转换条件
[选择分支] 右分支转换条件已内嵌在选择分支视图中，无需单独创建
System.Windows.Data Error: 40 : BindingExpression path error: 'Description' property not found on 'object' ''SFCBranchViewModel' (HashCode=52787457)'. BindingExpression:Path=SelectedElement.Description; DataItem='EnhancedSFCViewModel' (HashCode=2142393); target element is 'TextBlock' (Name=''); target property is 'Text' (type 'String')
[InitializeConnectPoints] 开始初始化连接点，ViewModel.Id: 9a712f73-0a34-408f-82d6-43eb465bd577
[InitializeConnectPoints] XAML连接点查找结果: LeftTop=True, LeftBottom=True, RightTop=True, RightBottom=True
[InitializeConnectPoints] LeftTop连接点ElementId: '', 期望: '9a712f73-0a34-408f-82d6-43eb465bd577'
[InitializeConnectPoints] ✅ 已修复LeftTop连接点ElementId: '9a712f73-0a34-408f-82d6-43eb465bd577'
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=52787457)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=52787457); target element is 'SFCConnectPoint' (Name='LeftTopConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=52787457)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=52787457); target element is 'SFCConnectPoint' (Name='LeftBottomConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=52787457)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=52787457); target element is 'SFCConnectPoint' (Name='TopConnectPoint1'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=52787457)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=52787457); target element is 'SFCConnectPoint' (Name='TopConnectPoint2'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=52787457)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=52787457); target element is 'SFCConnectPoint' (Name='LeftTopConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=52787457)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=52787457); target element is 'SFCConnectPoint' (Name='LeftParallelPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=52787457)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=52787457); target element is 'SFCConnectPoint' (Name='RightParallelPoint'); target property is 'NoTarget' (type 'Object')
[InitializeConnectPoints] 连接点已初始化，跳过重复初始化: 9a712f73-0a34-408f-82d6-43eb465bd577
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSelectionBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSiblingBranchCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertParallelBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
[连接点重叠检测] 点1: (250.0, 322.0), 点2: (242.0, 326.5), 距离: 9.2px, 重叠: True
[CreateConnectionPath] 连接 cdf77451-f16b-4a2a-8eb6-d69fc8a31f73 连接点重叠，隐藏连接线
[CreateConnectionPath] 起点: (250.0, 322.0), 终点: (242.0, 326.5)
连接 cdf77451-f16b-4a2a-8eb6-d69fc8a31f73 连接点重叠，使用连接点重叠方式
[AddConnection] 延迟更新连接线 cdf77451-f16b-4a2a-8eb6-d69fc8a31f73 的路径点
[CalculateElementConnectPoint] ✅ 选择分支ViewModel左上连接点(索引0): 250,326.5
[AddConnection] 延迟更新 - 源对象类型: SFCStepViewModel, 目标对象类型: SFCBranchViewModel
[AddConnection] 延迟更新 - 源位置: 200,200, 目标位置: 221,319.5
[AddConnection] 延迟更新后的路径点: 250,322 -> 250,326.5
[AddConnection] PathPoints集合已更新，应该触发重新评估
[SFCCanvas] 位置变化: SFCBranchViewModel.IsSelected
[SFCCanvas] 位置变化: SFCBranchViewModel.SelectedPart
[SFCSelectionBranchView] 右侧区域选中并开始拖拽: 9a712f73-0a34-408f-82d6-43eb465bd577
[SFCCanvas] 位置变化: SFCBranchViewModel.IsSelected
System.Windows.Data Error: 40 : BindingExpression path error: 'Description' property not found on 'object' ''SFCBranchViewModel' (HashCode=52787457)'. BindingExpression:Path=SelectedElement.Description; DataItem='EnhancedSFCViewModel' (HashCode=2142393); target element is 'TextBlock' (Name=''); target property is 'Text' (type 'String')
[InsertStepAfterSelected] 选中元素: 选择分支1(右侧转换条件)
[InsertStepAfterSelected] 选中元素位置: 221,319.5
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[GetActualConnectPointPosition] 分支连接点: 类型Selection, 索引3, 位置375,385.5
[CalculateConnectPointAlignedPositionDynamic] 动态对齐计算: 连接点375,385.5, 目标位置325,405.5
[InsertStepAfterSelected] 选择分支右侧使用动态计算: 连接点索引3, 步骤位置325,405.5
[InsertStepAfterSelected] 新步骤位置: 325,405.5
[InsertStepAfterSelected] 新步骤创建: d049e9f5-c079-423b-96a7-197039cdceed
[SFCCanvas] 为新创建的步骤添加位置变化监听: d049e9f5-c079-423b-96a7-197039cdceed
[InsertStepAfterSelected] 步骤已添加到集合
[InsertStepAfterSelected] 准备创建连接: 9a712f73-0a34-408f-82d6-43eb465bd577 -> d049e9f5-c079-423b-96a7-197039cdceed
[InsertStepAfterSelected] 使用选择分支右侧T1连接点: 9a712f73-0a34-408f-82d6-43eb465bd577[索引3]
[InsertStepAfterSelected] 开始创建连接: 9a712f73-0a34-408f-82d6-43eb465bd577 -> d049e9f5-c079-423b-96a7-197039cdceed, 源索引: 3
[AddConnection] 开始执行: 9a712f73-0a34-408f-82d6-43eb465bd577 -> d049e9f5-c079-423b-96a7-197039cdceed
[AddConnection] 对象查找结果: sourceObject=SFCBranchModel, targetObject=SFCStepModel
[AddConnection] 位置获取: 源ViewModel位置=221,319.5, 源Model位置=221,319.5
[AddConnection] 位置获取: 目标ViewModel位置=325,405.5, 目标Model位置=325,405.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Selection, 位置=221,319.5, 输出点=True
[CalculateElementConnectPoint] ✅ 选择分支Model T1转换条件下连接点(索引3): 375,385.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[AddConnection] 创建连接: 9a712f73-0a34-408f-82d6-43eb465bd577 -> d049e9f5-c079-423b-96a7-197039cdceed
[AddConnection] 源位置: 221,319.5, 目标位置: 325,405.5
[AddConnection] 源对象类型: SFCBranchModel, 目标对象类型: SFCStepModel
[AddConnection] 源连接点: 375,385.5, 目标连接点: 375,411.5
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 375,385.5, 终点: 375,411.5
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, IsInput: False, ConnectionId: 5fe7721e-0dc0-474d-85a7-62211c13e999
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCStepViewModel, IsInput: True, ConnectionId: 5fe7721e-0dc0-474d-85a7-62211c13e999
[UpdateConnectPointStates] 连接点状态更新完成: 9a712f73-0a34-408f-82d6-43eb465bd577[3] -> d049e9f5-c079-423b-96a7-197039cdceed[0]
[InsertStepAfterSelected] ✅ 连接创建成功: 5fe7721e-0dc0-474d-85a7-62211c13e999
[CreateConnectionPath] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 连接点不重叠，创建连接线
[CreateConnectionPath] 起点: (375.0, 385.5), 终点: (375.0, 411.5), 距离: 26.0px
[贝塞尔曲线创建] 起点: (375.0, 385.5), 终点: (375.0, 411.5)
[贝塞尔曲线创建] Y差异: 26.0px, X差异: 0.0px, 控制点偏移: 10.4px
[贝塞尔曲线创建] PathPoints[0]: (375.0, 385.5), PathPoints[1]: (375.0, 411.5)
添加连接线: 5fe7721e-0dc0-474d-85a7-62211c13e999
[AddConnection] 延迟更新连接线 5fe7721e-0dc0-474d-85a7-62211c13e999 的路径点
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[AddConnection] 延迟更新 - 源对象类型: SFCBranchViewModel, 目标对象类型: SFCStepViewModel
[AddConnection] 延迟更新 - 源位置: 221,319.5, 目标位置: 325,405.5
[AddConnection] 延迟更新后的路径点: 375,385.5 -> 375,411.5
[AddConnection] PathPoints集合已更新，应该触发重新评估
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 325,405.5
[SFCCanvas] 位置变化: SFCStepViewModel.IsSelected
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 325.8727272727273,405.5
[SFCCanvas] 位置变化: SFCStepViewModel.Position
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 375.8727272727273,411.5
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 329.3636363636364,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 379.3636363636364,411.5
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 330.23636363636365,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 380.23636363636365,411.5
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 332.8545454545455,404.6272727272727
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 382.8545454545455,410.6272727272727
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 333.72727272727275,404.6272727272727
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 383.72727272727275,410.6272727272727
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 335.4727272727273,404.6272727272727
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 385.4727272727273,410.6272727272727
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 336.3454545454546,404.6272727272727
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 386.3454545454546,410.6272727272727
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 338.9636363636363,404.6272727272727
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 388.9636363636363,410.6272727272727
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 339.83636363636344,404.6272727272727
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 389.83636363636344,410.6272727272727
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 342.45454545454515,404.6272727272727
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 392.45454545454515,410.6272727272727
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 342.45454545454504,404.6272727272727
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 392.45454545454504,410.6272727272727
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 343.3272727272722,404.6272727272727
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 393.3272727272722,410.6272727272727
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 344.19999999999936,404.6272727272727
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 394.19999999999936,410.6272727272727
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 345.9454545454538,404.6272727272727
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 395.9454545454538,410.6272727272727
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 345.9454545454537,404.6272727272727
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 395.9454545454537,410.6272727272727
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 345.9454545454536,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 395.9454545454536,411.5
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 346.81818181818073,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 396.81818181818073,411.5
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 348.56363636363517,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 398.56363636363517,411.5
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 349.43636363636233,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 399.43636363636233,411.5
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 349.4363636363622,405.5
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 399.4363636363622,411.5
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 349.4363636363621,406.3727272727273
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 399.4363636363621,412.3727272727273
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 350.30909090908926,406.3727272727273
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 400.30909090908926,412.3727272727273
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 350.30909090908915,406.3727272727273
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 400.30909090908915,412.3727272727273
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 351.1818181818163,406.3727272727273
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 401.1818181818163,412.3727272727273
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 351.1818181818162,406.3727272727273
[CalculateElementConnectPoint] ✅ 选择分支右侧T1转换条件下连接点(索引3): 375,385.5
[连接点调试] 源连接点计算位置: 375,385.5
[连接点调试] 目标连接点计算位置: 401.1818181818162,412.3727272727273
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 5fe7721e-0dc0-474d-85a7-62211c13e999 的PathPoints已更新，重新评估连接线
[SFCStepView] 拖动更新位置: d049e9f5-c079-423b-96a7-197039cdceed -> 352.05454545454336,406.3727272727273
