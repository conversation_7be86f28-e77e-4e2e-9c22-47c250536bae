classDiagram
    class UnifiedExecutionEngine {
        +UnifiedSFCBlueprintInterpreter interpreter
        +UnifiedSFCBlueprintCompiler compiler
        +JITOptimizer jitOptimizer
        +SharedExecutionContext sharedContext
        +IHardwareManager hardwareManager
        +SafetyMonitor safetyMonitor
        +ExecuteAsync(SFCModel) ExecutionResult
        +StartAsync() Task
        +StopAsync() Task
        +GetStatus() EngineStatus
    }
    
    class SharedExecutionContext {
        +Dictionary~string,object~ SFCVariables
        +Dictionary~string,object~ BlueprintVariables
        +HashSet~string~ ActiveSteps
        +Stack~BlueprintCallFrame~ CallStack
        +IHardwareManager HardwareManager
        +DateTime ExecutionStartTime
        +TimeSpan ElapsedTime
        +SyncSFCToBlueprintVariables(BlueprintCallInfo)
        +SyncBlueprintToSFCVariables(BlueprintCallInfo)
        +Reset()
        +Clone() SharedExecutionContext
    }
    
    class UnifiedSFCBlueprintInterpreter {
        +ExecuteAsync(SFCModel) ExecutionResult
        +EvaluateTransitionCondition(SFCTransitionModel) bool
        +ExecuteStepAsync(SFCStepModel) Task
        +CheckTransitionsAsync(List~SFCStepModel~) Task
        +InitializeExecutionContext(SFCModel)
        +UpdateExecutionState()
    }
    
    class UnifiedSFCBlueprintCompiler {
        +CompileAsync(SFCModel) CompilationResult
        +CollectReferencedBlueprintsAsync(SFCModel) List~BlueprintModel~
        +CompileBlueprintToMethodAsync(BlueprintModel) MethodInfo
        +CompileSFCToMainMethodAsync(SFCModel) MethodInfo
        +GenerateUnifiedClass(CompilationUnit) string
        +CompileToAssemblyAsync(string) Assembly
    }
    
    class IHardwareManager {
        <<interface>>
        +ConnectDeviceAsync(DeviceConfig) bool
        +DisconnectDeviceAsync(string) bool
        +GetDeviceStatusAsync(string) DeviceStatus
        +ConfigureProtocolAsync(string, ProtocolConfig) bool
        +ReadVariablesAsync(List~VariableAddress~) ReadResult
        +WriteVariablesAsync(List~VariableWrite~) WriteResult
        +GetCommunicationStatistics(string) CommunicationStatistics
    }
    
    class SafetyMonitor {
        +StartMonitoring() Task
        +StopMonitoring() Task
        +CheckWatchdog() bool
        +ValidateExecutionState(SharedExecutionContext) bool
        +HandleEmergencyStop() Task
        +GetSafetyStatus() SafetyStatus
    }
    
    class JITOptimizer {
        +AnalyzeHotspots(ExecutionTrace) HotspotAnalysis
        +OptimizeHotspot(string) OptimizationResult
        +SwitchExecutionMode(ExecutionMode) Task
        +GetPerformanceMetrics() PerformanceMetrics
    }
    
    UnifiedExecutionEngine --> SharedExecutionContext : uses
    UnifiedExecutionEngine --> UnifiedSFCBlueprintInterpreter : contains
    UnifiedExecutionEngine --> UnifiedSFCBlueprintCompiler : contains
    UnifiedExecutionEngine --> IHardwareManager : uses
    UnifiedExecutionEngine --> SafetyMonitor : contains
    UnifiedExecutionEngine --> JITOptimizer : contains
    
    UnifiedSFCBlueprintInterpreter --> SharedExecutionContext : uses
    UnifiedSFCBlueprintCompiler --> SharedExecutionContext : uses
    SafetyMonitor --> SharedExecutionContext : monitors
    JITOptimizer --> SharedExecutionContext : analyzes
