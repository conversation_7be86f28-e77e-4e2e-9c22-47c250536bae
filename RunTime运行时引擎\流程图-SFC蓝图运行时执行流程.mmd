flowchart TD
    Start([开始]) --> Load[项目加载<br/>SFC-蓝图模型读取]
    Load --> Validate[完整性验证<br/>安全性检查]
    Validate --> Init[环境初始化<br/>共享执行上下文创建]
    Init --> Hardware[硬件连接建立<br/>安全监控启动]
    Hardware --> Mode{选择执行模式}
    
    Mode -->|调试| Interpreter[解释器模式<br/>实时调试]
    Mode -->|生产| Compiler[编译器模式<br/>高性能执行]
    Mode -->|智能| JIT[JIT模式<br/>热点优化]
    
    Interpreter --> Cycle[扫描周期启动]
    Compiler --> Cycle
    JIT --> Cycle
    
    Cycle --> Safety[高优先级 1ms<br/>安全监控检查]
    Safety --> Control[中优先级 10ms<br/>SFC状态机执行<br/>蓝图逻辑执行<br/>硬件I/O处理]
    Control --> Monitor[低优先级 100ms<br/>性能监控<br/>调试支持<br/>UI更新]
    
    Monitor --> Check{继续执行?}
    Check -->|是| Safety
    Check -->|否| Stop([结束])
    
    subgraph "SFC-蓝图融合执行"
        SFC[SFC状态机] --> Blueprint[蓝图执行器]
        Blueprint --> Sync[变量同步]
        Sync --> SFC
    end
    
    Control -.-> SFC
    
    style Start fill:#4caf50,color:#fff
    style Stop fill:#f44336,color:#fff
    style Mode fill:#2196f3,color:#fff
    style Safety fill:#ff9800,color:#fff
    style Control fill:#9c27b0,color:#fff
    style Monitor fill:#607d8b,color:#fff
