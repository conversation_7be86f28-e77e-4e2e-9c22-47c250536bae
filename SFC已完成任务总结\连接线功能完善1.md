# SFC连接线问题修复报告

## 问题1：选择分支和步骤之间连接线有间隙

**问题原因**：
拖拽过程中使用`GetCenterPosition`方法获取连接点位置，该方法依赖WPF控件的实际布局状态，在快速拖拽时存在异步更新问题，导致连接点位置不稳定。

**涉及文件和方法**：
- `Controls\SFCCanvas.cs` - `UpdateConnectionLinePosition`方法
- `ViewModels\EnhancedSFCViewModel.cs` - `CalculateElementConnectPoint`方法

**修复方案**：
1. **改用稳定计算方法**：优先使用`CalculateElementConnectPoint`方法，基于元素位置和固定偏移量计算连接点位置
2. **修正偏移量**：将选择分支输入连接点X偏移量从`16+5`修正为`16+5+8`，匹配实际连接点位置
3. **保留回退机制**：当无法获取ViewModel时，仍使用`GetCenterPosition`作为回退方案

## 问题2：连接点重叠时不隐藏连接线

**问题原因**：
AddConnection方法中使用的元素位置与InsertStepAfterSelected中使用的位置不一致：
- InsertStepAfterSelected使用SelectedElement（ViewModel）的位置
- AddConnection使用从数据模型集合查找的对象（Model）的位置
- ViewModel和Model位置不同步，导致连接点计算错误，重叠检测失败

**涉及文件和方法**：
- `ViewModels\EnhancedSFCViewModel.cs` - `AddConnection`方法
- `ViewModels\EnhancedSFCViewModel.cs` - `CalculateConnectPointAlignedPosition`方法
- `ViewModels\EnhancedSFCViewModel.cs` - 新增`GetElementPositionFromViewModel`方法

**修复方案**：
1. **统一位置获取**：在AddConnection中优先使用ViewModel的位置，确保与UI显示一致
2. **修正Y坐标计算**：将步骤位置Y坐标从`transitionY+30`修正为`transitionY+24`，确保连接点精确重叠
3. **添加位置验证**：新增调试信息对比ViewModel和Model位置差异，确保位置同步

## 核心修复内容

### 1. 连接点重叠检测机制
**文件**：`Controls/SFCCanvas.cs` - `AreConnectPointsOverlapping`方法
```csharp
private bool AreConnectPointsOverlapping(Point point1, Point point2)
{
    const double connectPointRadius = 5.0;
    double distance = Math.Sqrt(Math.Pow(point2.X - point1.X, 2) + Math.Pow(point2.Y - point1.Y, 2));
    return distance < (connectPointRadius * 2);
}
```

### 2. 连接线创建时重叠检测
**文件**：`Controls/SFCCanvas.cs` - `CreateConnectionPath`方法
```csharp
if (AreConnectPointsOverlapping(startPoint, endPoint))
{
    return null; // 连接点重叠，不创建连接线
}
```

### 3. 连接线动态显示/隐藏
**文件**：`Controls/SFCCanvas.cs` - `UpdateConnectionLine`方法
- 连接点重叠时：隐藏连接线
- 连接点不重叠时：显示连接线
- 基于PathPoints集合变化事件自动更新

**修复结果**：
- 拖拽选择分支时连接线稳定，无间隙抖动
- 转换条件下方插入步骤时连接线正确隐藏，只显示重叠的连接点




# SFC连接线问题修复报告

## 问题1：选择分支和步骤之间连接线有间隙

**问题原因**：
拖拽过程中使用`GetCenterPosition`方法获取连接点位置，该方法依赖WPF控件的实际布局状态，在快速拖拽时存在异步更新问题，导致连接点位置不稳定。

**涉及文件和方法**：
- `Controls\SFCCanvas.cs` - `UpdateConnectionLinePosition`方法
- `ViewModels\EnhancedSFCViewModel.cs` - `CalculateElementConnectPoint`方法

**修复方案**：
1. **改用稳定计算方法**：优先使用`CalculateElementConnectPoint`方法，基于元素位置和固定偏移量计算连接点位置
2. **修正偏移量**：将选择分支输入连接点X偏移量从`16+5`修正为`16+5+8`，匹配实际连接点位置
3. **保留回退机制**：当无法获取ViewModel时，仍使用`GetCenterPosition`作为回退方案

## 问题2：连接点重叠时不隐藏连接线

**问题原因**：
AddConnection方法中使用的元素位置与InsertStepAfterSelected中使用的位置不一致：
- InsertStepAfterSelected使用SelectedElement（ViewModel）的位置
- AddConnection使用从数据模型集合查找的对象（Model）的位置
- ViewModel和Model位置不同步，导致连接点计算错误，重叠检测失败

**涉及文件和方法**：
- `ViewModels\EnhancedSFCViewModel.cs` - `AddConnection`方法
- `ViewModels\EnhancedSFCViewModel.cs` - `CalculateConnectPointAlignedPosition`方法
- `ViewModels\EnhancedSFCViewModel.cs` - 新增`GetElementPositionFromViewModel`方法

**修复方案**：
1. **统一位置获取**：在AddConnection中优先使用ViewModel的位置，确保与UI显示一致
2. **修正Y坐标计算**：将步骤位置Y坐标从`transitionY+30`修正为`transitionY+24`，确保连接点精确重叠
3. **添加位置验证**：新增调试信息对比ViewModel和Model位置差异，确保位置同步

**修复结果**：
- 拖拽选择分支时连接线稳定，无间隙抖动
- 转换条件下方插入步骤时连接线正确隐藏，只显示重叠的连接点



# 并行分支连接线修复报告

## 问题概述
并行分支插入后，左侧步骤S2错误地连接到了并行分支的右侧连接点，而不是应该连接的左侧连接点，导致连接关系混乱。

## 问题根本原因
**连接线更新过程中丢失连接点索引信息**：
1. **初始创建正确**：`AddConnection`方法正确传递连接点索引（左侧索引1，右侧索引0），计算出正确位置
2. **后续更新错误**：`UpdateConnectionsForElement`方法重新计算连接点位置时，未传递连接点索引参数，默认使用索引0
3. **位置被覆盖**：错误的连接点位置覆盖了`PathPoints`中的正确位置

## 修复方案

### 1. 修复连接点索引传递问题
**文件**：`Controls\SFCCanvas.cs` - `UpdateConnectionsForElement`方法

**修复前**：
```csharp
sourcePosition = viewModel.CalculateElementConnectPoint(sourceViewModel, sourceElementPosition, true);
targetPosition = viewModel.CalculateElementConnectPoint(targetViewModel, targetElementPosition, false);
```

**修复后**：
```csharp
sourcePosition = viewModel.CalculateElementConnectPoint(sourceViewModel, sourceElementPosition, true, connection.SourceConnectPointIndex);
targetPosition = viewModel.CalculateElementConnectPoint(targetViewModel, targetElementPosition, false, connection.TargetConnectPointIndex);
```

### 2. 恢复连接点重叠检测阈值
**文件**：`Controls\SFCCanvas.cs` - `AreConnectPointsOverlapping`方法

**修复**：恢复正确的10px重叠阈值（`connectPointRadius * 2`），确保连接点重叠时正确隐藏连接线

## 修复结果
1. **连接对应关系正确**：
   - 左侧步骤S2 → 并行分支左侧连接点（索引1）
   - 右侧步骤S3 → 并行分支右侧连接点（索引0）

2. **连接线显示逻辑正确**：
   - 连接点重叠时：隐藏连接线，只显示重叠的连接点
   - 连接点不重叠时：显示连接线，保持正确的左右对应关系

## 涉及文件
- `Controls\SFCCanvas.cs` - 连接点索引传递和重叠检测逻辑
- `ViewModels\EnhancedSFCViewModel.cs` - 连接点计算方法（已有正确实现）

## 技术要点
- **连接点索引定义**：并行分支左侧连接点索引1，右侧连接点索引0
- **重叠检测阈值**：10px（连接点半径5px × 2）
- **连接线更新机制**：确保所有连接点位置更新都使用正确的索引参数

至此，并行分支连接线的所有问题已完全修复，实现了正确的连接对应关系和连接线显示逻辑。


## 选择分支连接线修改位置：
### 1. 选择分支左侧上方连接点，与连接线末端的相对位置修改：
  - 在  ViewModels\EnhancedSFCViewModel.cs 第3578行：
    // 如果需要调整X坐标（向左减小，向右增大）
     var point = new Point(elementPosition.X + 16 + 5 , elementPosition.Y + 2 + 5);

    // 如果需要调整X坐标（向左减小，向右增大）
     var point = new Point(elementPosition.X + 16 + 5 , elementPosition.Y + 2 + 5);

### 2. 选择分支左侧下方连接点，与连接线末端的相对位置修改：
  - 在  ViewModels\EnhancedSFCViewModel.cs 第3585行：
    // 如果需要调整X坐标（向左减小，向右增大）
     var point = new Point(elementPosition.X + 16 + 5, elementPosition.Y + 30 + 5);

    // 如果需要调整Y坐标（向上减小，向下增大）  
     var point = new Point(elementPosition.X + 16 + 5, elementPosition.Y + 30 + 5);

### 3. 选择分支右侧上方连接点，与连接线末端的相对位置修改：
  - 在  ViewModels\EnhancedSFCViewModel.cs 第3592行：
    // 如果需要调整X坐标（向左减小，向右增大）
     var point = new Point(elementPosition.X + 162 + 5, elementPosition.Y + 30 + 5);

    // 如果需要调整Y坐标（向上减小，向下增大） 
     var point = new Point(elementPosition.X + 162 + 5, elementPosition.Y + 30 + 5);

### 4. 选择分支右侧下方连接点，与连接线末端的相对位置修改：
  - 在  ViewModels\EnhancedSFCViewModel.cs 第3599行：
    // 如果需要调整X坐标（向左减小，向右增大）
     var point = new Point(elementPosition.X + 162 + 5, elementPosition.Y + 61 + 5);

    // 如果需要调整Y坐标（向上减小，向下增大） 
     var point = new Point(elementPosition.X + 162 + 5, elementPosition.Y + 61 + 5);

### 5. 后续插入扩展选择分支，左侧下方连接点，与连接线末端的相对位置修改：
  - 在  ViewModels\EnhancedSFCViewModel.cs 第3678行：
    // 如果需要调整X坐标（向左减小，向右增大）
     var point = new Point(elementPosition.X + 16 + 5, elementPosition.Y + 2 + 5);

    // 如果需要调整Y坐标（向上减小，向下增大）
     var point = new Point(elementPosition.X + 16 + 5, elementPosition.Y + 2 + 5);