using System;
using System.Windows;
using PC_Control2.Demo.ViewModels;
using PC_Control2.Demo.Models;

namespace PC_Control2.Demo.Tests
{
    /// <summary>
    /// T1转换条件连接点位置验证测试
    /// </summary>
    public class T1ConnectPointTest
    {
        public static void RunTest()
        {
            Console.WriteLine("=== T1转换条件连接点位置验证测试 ===");
            
            // 创建选择分支
            var branch = new SFCBranchViewModel
            {
                Id = "TestBranch",
                Name = "测试选择分支",
                Position = new Point(100, 100),
                BranchType = SFCBranchType.Selection,
                SelectedPart = "Right" // 选中右侧部分
            };
            
            Console.WriteLine($"选择分支位置: {branch.Position}");
            Console.WriteLine($"选中部分: {branch.SelectedPart}");
            
            // 创建ViewModel来测试连接点计算
            var viewModel = new EnhancedSFCViewModel();
            
            // 测试T1转换条件下连接点计算
            var t1ConnectPoint = TestCalculateT1ConnectPoint(branch, branch.Position);
            Console.WriteLine($"T1转换条件下连接点: {t1ConnectPoint}");
            
            // 测试选择分支右侧下端连接点计算
            var branchConnectPoint = TestCalculateBranchConnectPoint(branch, branch.Position);
            Console.WriteLine($"选择分支右侧下端连接点: {branchConnectPoint}");
            
            // 计算差异
            var deltaX = t1ConnectPoint.X - branchConnectPoint.X;
            var deltaY = t1ConnectPoint.Y - branchConnectPoint.Y;
            Console.WriteLine($"位置差异: ΔX={deltaX:F1}, ΔY={deltaY:F1}");
            
            // 验证结果
            Console.WriteLine("\n=== 验证结果 ===");
            Console.WriteLine($"T1转换条件X坐标: {branch.Position.X} + 107 + (29+65)/2 = {branch.Position.X + 107 + (29 + 65) / 2.0}");
            Console.WriteLine($"选择分支右下X坐标: {branch.Position.X} + 162 + 5 = {branch.Position.X + 162 + 5}");
            Console.WriteLine($"预期X坐标差异: {(branch.Position.X + 107 + (29 + 65) / 2.0) - (branch.Position.X + 162 + 5)} 像素");
            
            if (Math.Abs(deltaX) > 20)
            {
                Console.WriteLine("⚠️  警告: T1连接点与选择分支连接点X坐标差异较大，可能影响视觉对齐");
            }
            else
            {
                Console.WriteLine("✅ T1连接点X坐标计算合理");
            }
        }
        
        private static Point TestCalculateT1ConnectPoint(SFCBranchViewModel branch, Point elementPosition)
        {
            // 模拟T1转换条件下连接点计算（索引3，右侧部分选中）
            if (branch.SelectedPart == "Right")
            {
                double t1CenterX = elementPosition.X + 107 + (29 + 65) / 2.0; // T1转换条件实际视觉中心X
                double t1BottomY = elementPosition.Y + 36 + 30;  // T1转换条件下端Y
                return new Point(t1CenterX, t1BottomY);
            }
            else
            {
                // 默认情况：右侧下端连接点
                return new Point(elementPosition.X + 162 + 5, elementPosition.Y + 61 + 5);
            }
        }
        
        private static Point TestCalculateBranchConnectPoint(SFCBranchViewModel branch, Point elementPosition)
        {
            // 选择分支右侧下端连接点（索引3，默认情况）
            return new Point(elementPosition.X + 162 + 5, elementPosition.Y + 61 + 5);
        }
    }
}
