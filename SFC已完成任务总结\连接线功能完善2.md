我理解您的决定，选择分支右侧连接线坐标计算确实是一个复杂的问题。现在让我使用memory MCP工具对顺控器元素连接线方法做一个全面的知识图谱总结。

让我重新创建实体：

让我尝试不同的方式创建知识图谱：

看起来memory工具有问题，让我直接为您提供一个详细的知识图谱总结：

# SFC顺控器连接线系统知识图谱

## 系统架构层

### SFC顺控器系统
- **定义**：基于西门子Graph标准的顺序功能图编辑器
- **架构**：WPF + MVVM模式
- **绘图引擎**：Canvas容器 + 自定义控件
- **核心文件**：EnhancedSFCViewModel.cs, SFCCanvas.cs

## 元素类型层

### 1. 步骤元素 (SFCStepViewModel)
- **标准尺寸**：90×117像素
- **连接点配置**：
  - 上连接点（输入）：Canvas.Left="45" Canvas.Top="1"
  - 下连接点（输出）：Canvas.Left="45" Canvas.Top="117"
- **索引系统**：输入/输出都使用索引0，通过isSource参数区分

### 2. 转换条件元素 (SFCTransitionViewModel)
- **标准尺寸**：74×30像素
- **连接点配置**：
  - 上连接点（输入）：Canvas.Left="55.3" Canvas.Top="-5"
  - 下连接点（输出）：Canvas.Left="55.3" Canvas.Top="25"
- **特殊属性**：支持条件表达式和始终为真模式

### 3. 选择分支元素 (SFCBranchViewModel)
- **复杂连接点系统**：
  - 索引0：左上连接点 Canvas.Left="16" Canvas.Top="2"
  - 索引1：左下连接点 Canvas.Left="16" Canvas.Top="30"
  - 索引2：右上连接点 Canvas.Left="162" Canvas.Top="30"
  - 索引3：右下连接点 Canvas.Left="162" Canvas.Top="61"
- **特殊问题**：右侧内嵌T1转换条件的连接点计算复杂

### 4. 并行分支元素 (SFCBranchViewModel)
- **双线连接点**：
  - 索引0：左上连接点 Canvas.Left="37" Canvas.Top="-9"
  - 索引1：左双线连接点 Canvas.Left="37" Canvas.Top="19.5"
  - 索引0（输出）：右双线连接点 Canvas.Left="183" Canvas.Top="19.5"

## 连接线创建方法层

### 1. 静态插入方法
- **触发场景**：用户通过菜单或快捷键插入元素
- **核心方法**：`InsertStepAfterSelected`, `InsertTransitionAfterSelected`
- **位置计算**：`CalculateConnectPointAlignedPositionDynamic`
- **连接创建**：`AddConnection(sourceId, targetId, sourceIndex, targetIndex)`

### 2. 动态拖拽方法
- **触发场景**：用户拖拽连接点创建连接
- **核心组件**：SFCConnectPointInteractionManager
- **实时计算**：鼠标移动时动态更新连接线路径
- **完成验证**：释放鼠标时验证连接有效性

## 连接点计算算法层

### 1. 核心计算方法
```csharp
// 主要计算方法
CalculateElementConnectPoint(element, position, isSource, connectPointIndex)

// 动态位置对齐
CalculateConnectPointAlignedPositionDynamic(sourceElement, targetSize, connectPointIndex)

// 实际连接点获取
GetActualConnectPointPosition(viewModel, connectPointIndex, isSource)
```

### 2. 坐标计算原理
- **基础公式**：元素位置 + Canvas偏移 + 连接点偏移 + 中心偏移(+5)
- **连接点尺寸**：10×10像素标准尺寸
- **中心对齐**：所有连接点都以中心点为准进行计算

### 3. 特殊处理逻辑
- **选择分支右侧**：需要区分是否选中右侧部分
- **T1转换条件**：内嵌在选择分支中的特殊连接点
- **并行分支**：双线连接点的特殊处理

## 连接验证层

### 1. 连接规则
- **步骤 → 转换条件**：步骤的输出连接点 → 转换条件的输入连接点
- **转换条件 → 步骤**：转换条件的输出连接点 → 步骤的输入连接点
- **分支连接**：根据分支类型和连接点索引进行验证

### 2. 验证方法
```csharp
// 连接验证
ConnectionValidationResult ValidateConnectionTo(SFCConnectPoint target)

// 适配器验证
SFCConnectPointAdapter.CanConnectTo(targetAdapter)
```

## 视觉渲染层

### 1. 连接线样式
- **标准连接线**：2像素宽度，灰色
- **选中状态**：3像素宽度，蓝色，发光效果
- **拖拽预览**：虚线样式，实时跟随鼠标

### 2. 路径计算
- **直线连接**：简单的点对点连接
- **路径点系统**：支持多点路径的复杂连接
- **自动避障**：避免与其他元素重叠

## 问题与挑战层

### 1. 已知问题
- **选择分支右侧T1转换条件**：连接点坐标计算不准确
- **静态动态不一致**：插入和拖拽使用不同的计算方法
- **复杂分支处理**：多层嵌套分支的连接点管理

### 2. 解决方案尝试
- **虚拟连接点机制**：为特殊情况创建虚拟连接点
- **上下文感知计算**：根据元素选中状态调整计算逻辑
- **统一计算接口**：尝试统一静态和动态计算方法

## 核心文件映射

### 1. ViewModel层
- **EnhancedSFCViewModel.cs**：主要的连接创建和管理逻辑
- **SFCConnectionViewModel.cs**：连接线的数据模型和属性

### 2. View层
- **SFCCanvas.cs**：画布控件，处理连接线的渲染和交互
- **SFCConnectPoint.xaml.cs**：连接点控件的交互逻辑

### 3. Model层
- **SFCConnectionModel.cs**：连接线的数据模型
- **SFCConnectPointAdapter.cs**：连接点适配器，处理连接验证

## 最佳实践

### 1. 连接创建流程
1. 确定源元素和目标元素
2. 计算连接点索引
3. 验证连接有效性
4. 创建连接模型
5. 更新视觉表示

### 2. 坐标计算原则
- 始终基于XAML中的Canvas定义
- 考虑连接点控件的实际尺寸
- 使用中心点对齐确保精确性
- 支持动态元素位置变化

### 3. 扩展性设计
- 连接点索引系统支持新元素类型
- 计算方法支持参数化配置
- 验证规则支持自定义扩展

这个知识图谱总结了SFC顺控器连接线系统的完整架构和实现细节，为后续的开发和维护提供了全面的参考。虽然选择分支右侧的连接点计算问题暂时搁置，但整个系统的其他部分都有清晰的实现方案和最佳实践。
